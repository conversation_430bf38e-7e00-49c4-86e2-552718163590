import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../features/calendar/controllers/calendar.controller.dart';
import '../../../features/home/<USER>/widgets/next_prayer_times.dart';
import '../extensions/string_extensions.dart';

class PrayerWidgetChannel {
  static const _channel =
      MethodChannel('com.perfectfit.qurankareem/prayer_widget');

  /// Call this right after you download / recalc calendarBySummerTime
  static Future<void> saveAllPrayerTimesForWidget() async {
    try {
      final calendarData = CalendarController.calendarBySummerTime;
      if (calendarData.isEmpty) {
        Log.w('Calendar data is empty, cannot save prayer times for widget');
        return;
      }

      final prefs = await SharedPreferences.getInstance();

      final now = DateTime.now();
      final sevenDays = now.add(const Duration(days: 7));

      final Map<String, Map<String, String>> allPrayerTimes = {};

      for (final cal in calendarData) {
        for (final day in cal.days) {
          if (day.gregorianDate.isEmpty) continue;
          final d = DateTime.tryParse(day.gregorianDate);
          if (d == null ||
              d.isBefore(now.subtract(const Duration(days: 1))) ||
              d.isAfter(sevenDays)) continue;

          allPrayerTimes[day.gregorianDate] = {
            'fajr': day.prayerTimes.fajr.convertTo12Hour,
            'dhuhr': day.prayerTimes.dhuhr.convertTo12Hour,
            'asr': day.prayerTimes.asr.convertTo12Hour,
            'maghrib': day.prayerTimes.maghrib.convertTo12Hour,
            'isha': day.prayerTimes.isha.convertTo12Hour,
            'sunrise': day.prayerTimes.sunrise.convertTo12Hour,
          };
        }
      }

      // Save bulk JSON
      await prefs.setString('allPrayerTimes', jsonEncode(allPrayerTimes));
      Log.i('Saved prayer times for ${allPrayerTimes.length} days');

      // Save TODAY's values for graceful fallback
      final calendarController = CalendarController(
        calendarRepo: CalendarController.calendarBySummerTime.first as dynamic,
      );
      final todayData = calendarController.currentDayCalendar.prayerTimes;

      await prefs.setString('fajr', todayData.fajr.convertTo12Hour);
      await prefs.setString('sunrise', todayData.sunrise.convertTo12Hour);
      await prefs.setString('dhuhr', todayData.dhuhr.convertTo12Hour);
      await prefs.setString('asr', todayData.asr.convertTo12Hour);
      await prefs.setString('maghrib', todayData.maghrib.convertTo12Hour);
      await prefs.setString('isha', todayData.isha.convertTo12Hour);

      final next = getNextPrayerTime(todayData, date: now);
      await prefs.setString('nextPrayer',
          '${next.name}\n${next.time.hour.toString().padLeft(2, '0')}:${next.time.minute.toString().padLeft(2, '0')}');
      await prefs.setString('titleText', 'الصلاة القادمة');

      Log.i('Saved today\'s prayer data and next prayer: ${next.name}');

      // Tell Android to rebuild the timeline / alarms
      await _channel.invokeMethod('scheduleWidget');
      Log.i('Android widget scheduling triggered successfully');
    } catch (e, stackTrace) {
      Log.e('Failed to save prayer times for widget: $e\n$stackTrace');
    }
  }

  /// Test connection to native Android
  static Future<bool> testConnection() async {
    try {
      final result = await _channel.invokeMethod('testConnection');
      return result ?? false;
    } catch (e) {
      Log.e('Failed to test connection: $e');
      return false;
    }
  }

  /// Update widgets manually
  static Future<bool> updateWidgets() async {
    try {
      final result = await _channel.invokeMethod('updateWidgets');
      return result ?? false;
    } catch (e) {
      Log.e('Failed to update widgets: $e');
      return false;
    }
  }
}
